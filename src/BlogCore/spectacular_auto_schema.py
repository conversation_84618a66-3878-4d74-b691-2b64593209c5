# myproject/spectacular_auto_schema.py
from drf_spectacular.openapi import AutoSchema
from drf_spectacular.types import OpenApiTypes
from rest_framework import serializers

class CustomAutoSchema(AutoSchema):
    def map_serializer_field(self, field):
        if isinstance(field, serializers.ImageField) or isinstance(field, serializers.FileField):
            return OpenApiTypes.BINARY
        return super().map_serializer_field(field)
